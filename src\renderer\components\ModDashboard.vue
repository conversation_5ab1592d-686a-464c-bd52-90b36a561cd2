<template>
  <div class="mod-dashboard">
    
    <!-- Filters and Search -->
    <div class="dashboard-controls">
      <div class="search-section">
        <div class="search-input-wrapper">
          <MagnifyingGlassIcon class="search-icon" />
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search mods by name, author, or content..."
            class="search-input"
          />
          <button v-if="searchQuery" @click="clearSearch" class="search-clear">
            <XMarkIcon />
          </button>
        </div>
      </div>
      
      <div class="filter-section">

        
        <div class="filter-group">
          <label class="filter-label">File Type</label>
          <select v-model="selectedFileTypeFilter" class="filter-select">
            <option value="">All Files</option>
            <option value=".package">Package Files</option>
            <option value=".ts4script">Script Files</option>
          </select>
        </div>
        
        <div class="filter-group">
          <label class="filter-label">Quality Range</label>
          <select v-model="selectedQualityFilter" class="filter-select">
            <option value="">All Quality</option>
            <option value="excellent">Excellent (90-100)</option>
            <option value="good">Good (70-89)</option>
            <option value="fair">Fair (50-69)</option>
            <option value="poor">Poor (0-49)</option>
          </select>
        </div>
        
        <div class="filter-group">
          <label class="filter-label">Sort By</label>
          <select v-model="selectedSortOption" class="filter-select">
            <option value="name">Name</option>
            <option value="quality">Quality Score</option>
            <option value="size">File Size</option>
            <option value="author">Author</option>
            <option value="intelligence">Intelligence Type</option>
          </select>
        </div>
        
        <button @click="clearAllFilters" class="filter-clear-btn">
          Clear Filters
        </button>
      </div>
    </div>
    
    <!-- Results Summary -->
    <div class="results-summary">
      <div class="results-summary__info">
        <span class="results-count">{{ filteredMods.length }}</span>
        <span class="results-text">of {{ totalMods }} mods</span>
        <span v-if="hasActiveFilters" class="results-filtered">(filtered)</span>
      </div>
      
      <div class="view-options">
        <button
          @click="viewMode = 'cards'"
          :class="{ active: viewMode === 'cards' }"
          class="view-option"
        >
          <Squares2X2Icon />
          Cards
        </button>
        <button
          @click="viewMode = 'list'"
          :class="{ active: viewMode === 'list' }"
          class="view-option"
        >
          <ListBulletIcon />
          List
        </button>
        <button
          @click="viewMode = 'table'"
          :class="{ active: viewMode === 'table' }"
          class="view-option"
        >
          <TableCellsIcon />
          Table
        </button>
        <button
          @click="viewMode = 'thumbnails'"
          :class="{ active: viewMode === 'thumbnails' }"
          class="view-option"
        >
          <PhotoIcon />
          Thumbnails
        </button>
      </div>
    </div>
    






    <!-- Loading State -->
    <div v-if="isLoading" class="loading-state">
      <div class="loading-spinner"></div>
      <p class="loading-text">Analyzing mods with advanced intelligence...</p>
    </div>

    <!-- Mod Grid/List -->
    <div v-if="filteredMods.length > 0" class="mod-results">
      <!-- Card View -->
      <div v-if="viewMode === 'cards'" class="mod-grid">
        <ModCard
          v-for="mod in paginatedMods"
          :key="mod.fileName"
          :mod-data="mod"
          :show-debug-info="showDebugMode"
        />
      </div>

      <!-- List View -->
      <div v-else-if="viewMode === 'list'" class="mod-list">
        <ModListItem
          v-for="mod in paginatedMods"
          :key="mod.fileName"
          :mod-data="mod"
        />
      </div>

      <!-- Thumbnail View -->
      <div v-else-if="viewMode === 'thumbnails'" class="thumbnail-view">
        <ThumbnailGrid
          :thumbnails="modThumbnails"
          :is-loading="isExtractingThumbnails"
          :loading-progress="thumbnailProgress"
          :filters="thumbnailFilters"
          @preview-thumbnail="openImagePreview"
          @show-mod-details="showModDetails"
          @thumbnail-load="onThumbnailLoad"
          @thumbnail-error="onThumbnailError"
        />
      </div>

      <!-- Table View -->
      <div v-else class="mod-table-container">
        <ModTable
          :mods="paginatedMods"
          :sort-field="selectedSortOption"
          @sort="handleSort"
        />
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="pagination">
        <button
          @click="currentPage = Math.max(1, currentPage - 1)"
          :disabled="currentPage === 1"
          class="pagination-btn"
        >
          Previous
        </button>

        <div class="pagination-info">
          Page {{ currentPage }} of {{ totalPages }}
        </div>

        <button
          @click="currentPage = Math.min(totalPages, currentPage + 1)"
          :disabled="currentPage === totalPages"
          class="pagination-btn"
        >
          Next
        </button>
      </div>
    </div>


    
    <!-- Empty State -->
    <div v-else class="empty-state">
      <div class="empty-state__icon">
        <FolderOpenIcon />
      </div>
      <h3 class="empty-state__title">No mods found</h3>
      <p class="empty-state__description">
        {{ hasActiveFilters ?
          'Try adjusting your filters to see more results.' :
          'Start by analyzing your Sims 4 mods folder.' }}
      </p>

      <button v-if="hasActiveFilters" @click="clearAllFilters" class="empty-state__action">
        Clear All Filters
      </button>
    </div>

    <!-- Debug Panel -->
    <div v-if="showDebugMode" class="debug-panel">
      <h4>Debug Information</h4>
      <pre>{{ debugInfo }}</pre>
    </div>

    <!-- Image Preview Modal -->
    <ImagePreview
      :is-open="showImagePreview"
      :thumbnails="modThumbnails"
      :initial-index="previewThumbnailIndex"
      @close="closeImagePreview"
      @show-mod-details="showModDetails"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  Squares2X2Icon,
  ListBulletIcon,
  TableCellsIcon,
  FolderOpenIcon,
  PhotoIcon
} from '@heroicons/vue/24/outline';

import ModCard from './ModCard.vue';
import ModListItem from './ModListItem.vue';
import ModTable from './ModTable.vue';
import ThumbnailGrid from './ThumbnailGrid.vue';
import ImagePreview from './ImagePreview.vue';

// Props
const props = defineProps<{
  mods?: any[];
  isLoading?: boolean;
  showDebugMode?: boolean;
}>();

// Set defaults manually since withDefaults is a compiler macro
const mods = computed(() => props.mods || []);
const isLoading = computed(() => props.isLoading || false);
const showDebugMode = computed(() => props.showDebugMode || false);



// Watch for prop changes
watch(() => props.mods, (newMods, oldMods) => {
  // Mods data changed - trigger reactivity
}, { immediate: true, deep: true });

// Watch for loading state changes
watch(() => props.isLoading, (newLoading, oldLoading) => {
  // Loading state changed - trigger reactivity
}, { immediate: true });

// Reactive state
const searchQuery = ref('');

const selectedFileTypeFilter = ref('');
const selectedQualityFilter = ref('');
const selectedSortOption = ref('name');
const viewMode = ref<'cards' | 'list' | 'table' | 'thumbnails'>('cards');
const currentPage = ref(1);
const itemsPerPage = ref(20);

// Thumbnail-related state
const modThumbnails = ref<any[]>([]);
const isExtractingThumbnails = ref(false);
const thumbnailProgress = ref(0);
const showImagePreview = ref(false);
const previewThumbnailIndex = ref(0);
const thumbnailFilters = computed(() => ({
  category: selectedFileTypeFilter.value,
  search: searchQuery.value
}));

// Computed properties
const totalMods = computed(() => {
  return mods.value?.length || 0;
});



const filteredMods = computed(() => {
  if (!mods.value || !Array.isArray(mods.value)) {
    return [];
  }

  let filtered = [...mods.value];

  // Search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(mod =>
      mod.fileName.toLowerCase().includes(query) ||
      (mod.author && mod.author.toLowerCase().includes(query)) ||
      (mod.modName && mod.modName.toLowerCase().includes(query))
    );
  }



  // File type filter
  if (selectedFileTypeFilter.value) {
    filtered = filtered.filter(mod =>
      mod.fileExtension === selectedFileTypeFilter.value
    );
  }

  // Quality filter
  if (selectedQualityFilter.value) {
    filtered = filtered.filter(mod => {
      if (!mod.qualityScore) return false;
      const score = mod.qualityScore;
      switch (selectedQualityFilter.value) {
        case 'excellent': return score >= 90;
        case 'good': return score >= 70 && score < 90;
        case 'fair': return score >= 50 && score < 70;
        case 'poor': return score < 50;
        default: return true;
      }
    });
  }

  // Sort
  filtered.sort((a, b) => {
    switch (selectedSortOption.value) {
      case 'name':
        return a.fileName.localeCompare(b.fileName);
      case 'quality':
        return (b.qualityScore || 0) - (a.qualityScore || 0);
      case 'size':
        return (b.fileSize || 0) - (a.fileSize || 0);
      case 'author':
        return (a.author || '').localeCompare(b.author || '');
      case 'intelligence':
        return a.intelligenceType.localeCompare(b.intelligenceType);
      default:
        return 0;
    }
  });

  return filtered;
});

const totalPages = computed(() => 
  Math.ceil(filteredMods.value.length / itemsPerPage.value)
);

const paginatedMods = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filteredMods.value.slice(start, end);
});

const hasActiveFilters = computed(() =>
  searchQuery.value ||
  selectedFileTypeFilter.value ||
  selectedQualityFilter.value
);

const debugInfo = computed(() => ({
  totalMods: totalMods.value,
  filteredMods: filteredMods.value.length,
  currentFilters: {
    search: searchQuery.value,
    fileType: selectedFileTypeFilter.value,
    quality: selectedQualityFilter.value
  },
  viewMode: viewMode.value,
  currentPage: currentPage.value
}));

// Watch for filtered mods changes (moved here after computed properties are defined)
watch(filteredMods, (newFiltered, oldFiltered) => {
  // Filtered mods changed - trigger reactivity
}, { immediate: true });

// Methods
const clearSearch = () => {
  searchQuery.value = '';
};

const clearAllFilters = () => {
  searchQuery.value = '';
  selectedFileTypeFilter.value = '';
  selectedQualityFilter.value = '';
  currentPage.value = 1;
};

const handleSort = (field: string) => {
  selectedSortOption.value = field;
  currentPage.value = 1;
};

const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

// Watch for filter changes to reset pagination
const resetPagination = () => {
  currentPage.value = 1;
};

// Thumbnail methods
const openImagePreview = (thumbnail: any) => {
  const index = modThumbnails.value.findIndex(t => t.id === thumbnail.id);
  if (index !== -1) {
    previewThumbnailIndex.value = index;
    showImagePreview.value = true;
  }
};

const closeImagePreview = () => {
  showImagePreview.value = false;
};

const showModDetails = (thumbnail: any) => {
  // Find the corresponding mod and show its details
  const mod = mods.value.find(m => m.fileName === thumbnail.modFileName);
  if (mod) {
    // Could emit an event or show a modal with mod details
    console.log('Show mod details for:', mod);
  }
};

const onThumbnailLoad = (thumbnail: any) => {
  // Handle successful thumbnail load
  console.log('Thumbnail loaded:', thumbnail.modFileName);
};

const onThumbnailError = (thumbnail: any) => {
  // Handle thumbnail load error
  console.warn('Failed to load thumbnail for:', thumbnail.modFileName);
};

const extractThumbnails = async () => {
  if (!mods.value || mods.value.length === 0) return;

  isExtractingThumbnails.value = true;
  thumbnailProgress.value = 0;
  modThumbnails.value = [];

  try {
    // Import the thumbnail extraction service
    const { ThumbnailExtractionService } = await import('../../services/visual/ThumbnailExtractionService');

    const totalMods = mods.value.length;
    let processedMods = 0;

    for (const mod of mods.value) {
      try {
        // For now, create mock thumbnails since we don't have actual file buffers
        const mockThumbnail = {
          id: `thumb_${mod.fileName}_${Date.now()}`,
          modFileName: mod.fileName,
          resourceType: 'mock',
          resourceKey: 'mock_key',
          imageData: createMockThumbnailData(mod),
          format: 'svg' as const,
          width: 200,
          height: 200,
          category: mod.category || 'unknown',
          subcategory: mod.subcategory,
          confidence: 0.8,
          extractionMethod: 'fallback_icon' as const,
          fileSize: mod.fileSize || 0,
          isHighQuality: false,
          isFallback: true
        };

        modThumbnails.value.push(mockThumbnail);

      } catch (error) {
        console.warn(`Failed to extract thumbnail for ${mod.fileName}:`, error);
      }

      processedMods++;
      thumbnailProgress.value = (processedMods / totalMods) * 100;
    }

  } catch (error) {
    console.error('Error extracting thumbnails:', error);
  } finally {
    isExtractingThumbnails.value = false;
  }
};

const createMockThumbnailData = (mod: any): string => {
  const category = mod.category || 'unknown';
  const icons: Record<string, string> = {
    'cas': '👕',
    'objects': '🪑',
    'script': '⚙️',
    'tuning': '🔧',
    'unknown': '📦'
  };

  const icon = icons[category] || icons.unknown;
  const color = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'][Math.floor(Math.random() * 5)];

  const svg = `<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
    <rect width="100%" height="100%" fill="${color}" rx="8"/>
    <text x="50%" y="40%" text-anchor="middle" font-size="48">${icon}</text>
    <text x="50%" y="70%" text-anchor="middle" dy=".3em" fill="white" font-family="Arial" font-size="12">
      ${category.toUpperCase()}
    </text>
  </svg>`;

  return `data:image/svg+xml;base64,${btoa(svg)}`;
};

// Watch for view mode changes to extract thumbnails
watch(viewMode, (newMode) => {
  if (newMode === 'thumbnails' && modThumbnails.value.length === 0) {
    extractThumbnails();
  }
});

// Lifecycle
onMounted(() => {
  // Any initialization logic
});
</script>

<style scoped>
.mod-dashboard {
  min-height: 100vh;
  background: var(--bg-secondary);
}

/* Main Dashboard Content */



/* Controls */
.dashboard-controls {
  max-width: 1280px;
  margin: 0 auto;
  padding: var(--space-6);
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.search-section {
  display: flex;
  justify-content: center;
}

.search-input-wrapper {
  position: relative;
  width: 100%;
  max-width: 600px;
}

.search-icon {
  position: absolute;
  left: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: var(--text-tertiary);
}

.search-input {
  width: 100%;
  height: 56px;
  padding: 0 var(--space-12) 0 var(--space-12);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-full);
  background: var(--bg-elevated);
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  transition: all var(--duration-200) var(--ease-out);
  box-shadow: var(--shadow-sm);
}

.search-input::placeholder {
  color: var(--text-tertiary);
  font-weight: var(--font-medium);
}

.search-input:focus {
  outline: none;
  border-color: var(--plumbob-green);
  box-shadow: 0 0 0 4px var(--plumbob-green-bg), var(--shadow-md);
  background: var(--bg-primary);
}

.search-clear {
  position: absolute;
  right: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  color: var(--text-tertiary);
  cursor: pointer;
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-clear:hover {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

.filter-section {
  display: flex;
  gap: var(--space-4);
  align-items: end;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.filter-label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

.filter-select {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  font-size: var(--text-sm);
  color: var(--text-primary);
  min-width: 150px;
}

.filter-clear-btn {
  padding: var(--space-2) var(--space-4);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-out);
}

.filter-clear-btn:hover {
  background: var(--bg-secondary);
  border-color: var(--border-strong);
}

/* Results */
.results-summary {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-6) var(--space-4) var(--space-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.results-summary__info {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
}

.results-count {
  font-weight: var(--font-bold);
  color: var(--text-primary);
  font-family: var(--font-family-mono);
}

.results-text {
  color: var(--text-secondary);
}

.results-filtered {
  color: var(--text-accent);
  font-weight: var(--font-medium);
}

.view-options {
  display: flex;
  gap: var(--space-1);
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--space-1);
}

.view-option {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-2) var(--space-3);
  border: none;
  background: none;
  color: var(--text-secondary);
  font-size: var(--text-sm);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-out);
}

.view-option:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.view-option.active {
  background: var(--sims-blue);
  color: var(--text-inverse);
}

.view-option svg {
  width: 16px;
  height: 16px;
}

/* Content */
.mod-results {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-6) var(--space-6) var(--space-6);
}

.mod-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: var(--space-6);
}

.mod-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.mod-table-container {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  overflow: hidden;
}

/* Loading */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-16);
  text-align: center;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--border-light);
  border-top: 4px solid var(--sims-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  margin: 0;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-16);
  text-align: center;
}

.empty-state__icon {
  width: 64px;
  height: 64px;
  color: var(--text-tertiary);
  margin-bottom: var(--space-4);
}

.empty-state__title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

.empty-state__description {
  font-size: var(--text-base);
  color: var(--text-secondary);
  margin: 0 0 var(--space-6) 0;
  max-width: 400px;
}

.empty-state__action {
  padding: var(--space-3) var(--space-6);
  background: var(--sims-blue);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-out);
}

.empty-state__action:hover {
  background: var(--sims-blue-dark);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-stats {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-3);
  }

  .stat-card {
    padding: var(--space-4);
  }

  .stat-card__value {
    font-size: var(--text-3xl);
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    padding: var(--space-4);
  }

  .dashboard-controls {
    padding: var(--space-4);
  }

  .dashboard-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-2);
  }

  .stat-card {
    padding: var(--space-3);
  }

  .stat-card__value {
    font-size: var(--text-2xl);
  }

  .stat-card__label {
    font-size: 10px;
  }

  .search-input-wrapper {
    max-width: none;
  }

  .search-input {
    height: 48px;
    font-size: var(--text-base);
  }

  .filter-section {
    flex-direction: column;
    gap: var(--space-3);
  }

  .filter-group {
    flex-direction: column;
    gap: var(--space-2);
  }

  .mod-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .results-summary {
    padding: 0 var(--space-4) var(--space-3) var(--space-4);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }

  .view-options {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    padding: var(--space-3);
  }

  .dashboard-title__main {
    font-size: var(--text-2xl);
  }

  .dashboard-title__subtitle {
    font-size: var(--text-sm);
  }

  .dashboard-stats {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: var(--space-2);
  }

  .stat-card__value {
    font-size: var(--text-xl);
  }

  .search-input {
    height: 44px;
    padding: 0 var(--space-10) 0 var(--space-10);
  }

  .search-icon {
    left: var(--space-2);
    width: 18px;
    height: 18px;
  }

  .mod-results {
    padding: 0 var(--space-3) var(--space-3) var(--space-3);
  }
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--space-4);
  margin-top: var(--space-8);
}

.pagination-btn {
  padding: var(--space-2) var(--space-4);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-out);
}

.pagination-btn:hover:not(:disabled) {
  background: var(--bg-secondary);
  border-color: var(--border-medium);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-family: var(--font-family-mono);
}
</style>
