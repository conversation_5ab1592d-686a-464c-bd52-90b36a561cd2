/**
 * Thumbnail Extraction Service
 * 
 * Extracts thumbnails from Sims 4 mod files for visual category browsing.
 * Supports multiple image formats and provides web-compatible thumbnail generation.
 * 
 * Addresses Reddit request: "visual categories with thumbnails and sidebar navigation"
 */

import { Package } from '@s4tk/models';
import { DdsImageResource } from '@s4tk/models';
import { BinaryResourceType } from '@s4tk/models/enums';
import type { ResourceEntry } from '@s4tk/models/types';
import { PackageParserService } from '../shared/PackageParserService';
import { RESOURCE_GROUPS } from '../../constants/ResourceTypeRegistry';

export interface ThumbnailData {
    id: string;
    modFileName: string;
    resourceType: string;
    resourceKey: string;
    imageData: string; // Base64 encoded image data or data URL
    format: 'png' | 'jpg' | 'webp' | 'svg';
    width: number;
    height: number;
    category: string;
    subcategory?: string;
    confidence: number;
    extractionMethod: 'cas_thumbnail' | 'dds_conversion' | 'dst_conversion' | 'png_direct' | 'fallback_icon';
    fileSize: number;
    isHighQuality: boolean;
    isFallback?: boolean;
}

export interface ThumbnailExtractionResult {
    success: boolean;
    thumbnails: ThumbnailData[];
    errors: string[];
    processingTime: number;
    totalImagesFound: number;
    thumbnailsExtracted: number;
}

export interface ThumbnailExtractionOptions {
    maxThumbnails?: number;
    preferredFormat?: 'png' | 'jpg' | 'webp';
    maxWidth?: number;
    maxHeight?: number;
    quality?: number; // 0-100 for JPEG/WebP
    prioritizeCasThumbnails?: boolean;
    includeTextures?: boolean;
    generateFallbacks?: boolean;
}

/**
 * Service for extracting thumbnails from Sims 4 mod packages
 */
export class ThumbnailExtractionService {
    private static readonly DEFAULT_OPTIONS: Required<ThumbnailExtractionOptions> = {
        maxThumbnails: 10,
        preferredFormat: 'webp',
        maxWidth: 256,
        maxHeight: 256,
        quality: 85,
        prioritizeCasThumbnails: true,
        includeTextures: true,
        generateFallbacks: true
    };

    /**
     * Extracts thumbnails from a mod package buffer
     */
    public static async extractThumbnails(
        packageBuffer: Buffer,
        fileName: string,
        options: ThumbnailExtractionOptions = {}
    ): Promise<ThumbnailExtractionResult> {
        const startTime = performance.now();
        const opts = { ...this.DEFAULT_OPTIONS, ...options };
        
        const result: ThumbnailExtractionResult = {
            success: false,
            thumbnails: [],
            errors: [],
            processingTime: 0,
            totalImagesFound: 0,
            thumbnailsExtracted: 0
        };

        try {
            // Parse the package using shared service
            const parseResult = PackageParserService.parsePackage(packageBuffer, fileName, {
                decompressBuffers: true,
                loadRaw: false,
                enableCaching: true
            });

            // Find all image resources using shared resource groups
            const imageResources = PackageParserService.getResourcesByType(
                parseResult.package,
                RESOURCE_GROUPS.THUMBNAIL_RESOURCES
            );
            result.totalImagesFound = imageResources.length;

            if (imageResources.length === 0) {
                result.errors.push('No image resources found in package');
                result.processingTime = performance.now() - startTime;
                return result;
            }

            // Sort resources by priority (CAS thumbnails first if preferred)
            const sortedResources = this.sortResourcesByPriority(imageResources, opts);

            // Extract thumbnails up to the limit
            const extractionPromises = sortedResources
                .slice(0, opts.maxThumbnails)
                .map(resource => this.extractThumbnailFromResource(resource, fileName, opts));

            const thumbnailResults = await Promise.allSettled(extractionPromises);

            // Process results
            for (const thumbnailResult of thumbnailResults) {
                if (thumbnailResult.status === 'fulfilled' && thumbnailResult.value) {
                    result.thumbnails.push(thumbnailResult.value);
                    result.thumbnailsExtracted++;
                } else if (thumbnailResult.status === 'rejected') {
                    result.errors.push(thumbnailResult.reason?.message || 'Unknown extraction error');
                }
            }

            result.success = result.thumbnails.length > 0;
            result.processingTime = performance.now() - startTime;

            return result;

        } catch (error) {
            result.errors.push(`Package parsing failed: ${error.message}`);
            result.processingTime = performance.now() - startTime;
            return result;
        }
    }

    // Removed findImageResources and isImageResourceType methods - now using shared ResourceTypeRegistry

    /**
     * Sorts resources by extraction priority
     */
    private static sortResourcesByPriority(
        resources: ResourceEntry[], 
        options: Required<ThumbnailExtractionOptions>
    ): ResourceEntry[] {
        return resources.sort((a, b) => {
            const aPriority = this.getResourcePriority(a.key.type, options);
            const bPriority = this.getResourcePriority(b.key.type, options);
            return bPriority - aPriority; // Higher priority first
        });
    }

    /**
     * Gets priority score for a resource type
     */
    private static getResourcePriority(resourceType: number, options: Required<ThumbnailExtractionOptions>): number {
        if (options.prioritizeCasThumbnails && resourceType === BinaryResourceType.CasPartThumbnail) {
            return 100; // Highest priority for CAS thumbnails
        }
        
        switch (resourceType) {
            case BinaryResourceType.PngImage: return 80;
            case BinaryResourceType.DdsImage: return 70;
            case BinaryResourceType.DstImage: return 60;
            case BinaryResourceType.RlesImage: return 50;
            case BinaryResourceType.Rle2Image: return 40;
            default: return 0;
        }
    }

    /**
     * Extracts a thumbnail from a specific resource
     */
    private static async extractThumbnailFromResource(
        resource: ResourceEntry,
        fileName: string,
        options: Required<ThumbnailExtractionOptions>
    ): Promise<ThumbnailData | null> {
        const resourceType = resource.key.type;
        
        try {
            switch (resourceType) {
                case BinaryResourceType.CasPartThumbnail:
                    return await this.extractFromCasThumbnail(resource, fileName, options);
                
                case BinaryResourceType.DdsImage:
                    return await this.extractFromDdsImage(resource, fileName, options);
                
                case BinaryResourceType.PngImage:
                    return await this.extractFromPngImage(resource, fileName, options);
                
                default:
                    if (options.generateFallbacks) {
                        return await this.extractGenericImage(resource, fileName, options);
                    }
                    return null;
            }
        } catch (error) {
            console.warn(`Failed to extract thumbnail from resource ${resource.key.type}:`, error);
            return null;
        }
    }

    /**
     * Extracts thumbnail from CAS Part Thumbnail resource
     */
    private static async extractFromCasThumbnail(
        resource: ResourceEntry,
        fileName: string,
        options: Required<ThumbnailExtractionOptions>
    ): Promise<ThumbnailData | null> {
        // CAS thumbnails are typically already in a usable format
        const imageData = this.bufferToBase64(resource.value as Buffer, 'image/png');
        
        return {
            id: `${fileName}-cas-${resource.key.instance.toString(16)}`,
            modFileName: fileName,
            resourceType: 'CAS Thumbnail',
            resourceKey: `${resource.key.type.toString(16)}-${resource.key.group.toString(16)}-${resource.key.instance.toString(16)}`,
            imageData,
            format: 'png',
            width: 256, // Standard CAS thumbnail size
            height: 256,
            category: 'Create-a-Sim',
            confidence: 95,
            extractionMethod: 'cas_thumbnail',
            fileSize: (resource.value as Buffer).length,
            isHighQuality: true
        };
    }

    /**
     * Extracts thumbnail from DDS Image resource
     */
    private static async extractFromDdsImage(
        resource: ResourceEntry,
        fileName: string,
        options: Required<ThumbnailExtractionOptions>
    ): Promise<ThumbnailData | null> {
        try {
            const ddsResource = DdsImageResource.from(resource.value as Buffer);
            const imageInfo = ddsResource.image;
            
            if (!imageInfo) {
                throw new Error('No image data found in DDS resource');
            }

            // Convert DDS to web-compatible format
            const imageData = this.convertDdsToWebFormat(imageInfo, options);
            
            return {
                id: `${fileName}-dds-${resource.key.instance.toString(16)}`,
                modFileName: fileName,
                resourceType: 'DDS Image',
                resourceKey: `${resource.key.type.toString(16)}-${resource.key.group.toString(16)}-${resource.key.instance.toString(16)}`,
                imageData,
                format: options.preferredFormat,
                width: imageInfo.width || 256,
                height: imageInfo.height || 256,
                category: 'Texture',
                confidence: 85,
                extractionMethod: 'dds_conversion',
                fileSize: (resource.value as Buffer).length,
                isHighQuality: (imageInfo.width || 0) >= 256 && (imageInfo.height || 0) >= 256
            };
        } catch (error) {
            console.warn('Failed to parse DDS image:', error);
            return null;
        }
    }

    /**
     * Extracts thumbnail from PNG Image resource
     */
    private static async extractFromPngImage(
        resource: ResourceEntry,
        fileName: string,
        options: Required<ThumbnailExtractionOptions>
    ): Promise<ThumbnailData | null> {
        const imageData = this.bufferToBase64(resource.value as Buffer, 'image/png');
        
        return {
            id: `${fileName}-png-${resource.key.instance.toString(16)}`,
            modFileName: fileName,
            resourceType: 'PNG Image',
            resourceKey: `${resource.key.type.toString(16)}-${resource.key.group.toString(16)}-${resource.key.instance.toString(16)}`,
            imageData,
            format: 'png',
            width: 256, // Will be determined by actual image analysis
            height: 256,
            category: 'Image',
            confidence: 90,
            extractionMethod: 'png_direct',
            fileSize: (resource.value as Buffer).length,
            isHighQuality: true
        };
    }

    /**
     * Generic image extraction for other formats
     */
    private static async extractGenericImage(
        resource: ResourceEntry,
        fileName: string,
        options: Required<ThumbnailExtractionOptions>
    ): Promise<ThumbnailData | null> {
        // Placeholder for other image formats
        // This would need specific implementation for DST, RLES, etc.
        return null;
    }

    /**
     * Converts a buffer to base64 data URL
     */
    private static bufferToBase64(buffer: Buffer, mimeType: string): string {
        const base64 = buffer.toString('base64');
        return `data:${mimeType};base64,${base64}`;
    }

    /**
     * Converts DDS image data to web-compatible format
     */
    private static convertDdsToWebFormat(imageInfo: any, options: Required<ThumbnailExtractionOptions>): string {
        // This is a placeholder - actual DDS conversion would require additional libraries
        // For now, we'll return a placeholder or attempt basic conversion
        
        // In a real implementation, you would:
        // 1. Parse DDS header for format information
        // 2. Decompress DDS data if compressed
        // 3. Convert to RGB/RGBA format
        // 4. Resize if needed
        // 5. Encode as PNG/JPEG/WebP
        
        // For now, return a placeholder
        const placeholderSvg = `<svg width="256" height="256" xmlns="http://www.w3.org/2000/svg">
            <rect width="256" height="256" fill="#4CAF50"/>
            <text x="128" y="128" text-anchor="middle" fill="white" font-family="Arial" font-size="16">DDS Image</text>
        </svg>`;
        
        return `data:image/svg+xml;base64,${Buffer.from(placeholderSvg).toString('base64')}`;
    }
}
